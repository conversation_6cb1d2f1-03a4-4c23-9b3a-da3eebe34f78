<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scalper App</title>
    <!-- Add your CSS here -->
</head>
<body>
    <div class="container">
        <h1>Scalper App</h1>
        <!-- Your app content here -->
        
        <button onclick="navigateTo('about')">About</button>
    </div>

    <script>
        // Navigation function
        function navigateTo(page) {
            if (window.Android) {
                window.Android.switchPage(page);
            } else {
                window.location.href = page + '.html';
            }
        }
        
        // History management functions
        function saveToAndroidStorage(historyType, data) {
            if (window.Android) {
                window.Android.saveHistory(historyType, JSON.stringify(data));
            } else {
                localStorage.setItem(historyType, JSON.stringify(data));
            }
        }
        
        function loadFromAndroidStorage(historyType) {
            if (window.Android) {
                return JSON.parse(window.Android.loadHistory(historyType));
            } else {
                const data = localStorage.getItem(historyType);
                return data ? JSON.parse(data) : [];
            }
        }
    </script>
</body>
</html>

