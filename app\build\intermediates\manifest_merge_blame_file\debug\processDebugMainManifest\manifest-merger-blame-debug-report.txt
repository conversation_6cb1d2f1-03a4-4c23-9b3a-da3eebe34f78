1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.scalper"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\Documents\apk\scalper\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\Users\<USER>\Documents\apk\scalper\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\Users\<USER>\Documents\apk\scalper\app\src\main\AndroidManifest.xml:6:5-79
12-->C:\Users\<USER>\Documents\apk\scalper\app\src\main\AndroidManifest.xml:6:22-76
13
14    <permission
14-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ac9a5a6a36d139f4d41d9656ee6c4dd\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
15        android:name="com.example.scalper.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
15-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ac9a5a6a36d139f4d41d9656ee6c4dd\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
16        android:protectionLevel="signature" />
16-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ac9a5a6a36d139f4d41d9656ee6c4dd\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
17
18    <uses-permission android:name="com.example.scalper.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
18-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ac9a5a6a36d139f4d41d9656ee6c4dd\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
18-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ac9a5a6a36d139f4d41d9656ee6c4dd\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
19
20    <application
20-->C:\Users\<USER>\Documents\apk\scalper\app\src\main\AndroidManifest.xml:8:5-20:19
21        android:allowBackup="true"
21-->C:\Users\<USER>\Documents\apk\scalper\app\src\main\AndroidManifest.xml:9:9-35
22        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
22-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ac9a5a6a36d139f4d41d9656ee6c4dd\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
23        android:dataExtractionRules="@xml/data_extraction_rules"
23-->C:\Users\<USER>\Documents\apk\scalper\app\src\main\AndroidManifest.xml:10:9-65
24        android:debuggable="true"
25        android:extractNativeLibs="false"
26        android:fullBackupContent="@xml/backup_rules"
26-->C:\Users\<USER>\Documents\apk\scalper\app\src\main\AndroidManifest.xml:11:9-54
27        android:icon="@mipmap/ic_launcher"
27-->C:\Users\<USER>\Documents\apk\scalper\app\src\main\AndroidManifest.xml:12:9-43
28        android:label="@string/app_name"
28-->C:\Users\<USER>\Documents\apk\scalper\app\src\main\AndroidManifest.xml:13:9-41
29        android:roundIcon="@mipmap/ic_launcher_round"
29-->C:\Users\<USER>\Documents\apk\scalper\app\src\main\AndroidManifest.xml:14:9-54
30        android:supportsRtl="true"
30-->C:\Users\<USER>\Documents\apk\scalper\app\src\main\AndroidManifest.xml:15:9-35
31        android:theme="@style/Theme.Scalper"
31-->C:\Users\<USER>\Documents\apk\scalper\app\src\main\AndroidManifest.xml:16:9-45
32        android:usesCleartextTraffic="true" >
32-->C:\Users\<USER>\Documents\apk\scalper\app\src\main\AndroidManifest.xml:17:9-44
33
34        <!-- Activity declaration -->
35        <provider
35-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc7c2f5ce3deab900ab6468ebed00762\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
36            android:name="androidx.startup.InitializationProvider"
36-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc7c2f5ce3deab900ab6468ebed00762\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
37            android:authorities="com.example.scalper.androidx-startup"
37-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc7c2f5ce3deab900ab6468ebed00762\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
38            android:exported="false" >
38-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc7c2f5ce3deab900ab6468ebed00762\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
39            <meta-data
39-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc7c2f5ce3deab900ab6468ebed00762\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
40                android:name="androidx.emoji2.text.EmojiCompatInitializer"
40-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc7c2f5ce3deab900ab6468ebed00762\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
41                android:value="androidx.startup" />
41-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc7c2f5ce3deab900ab6468ebed00762\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
42            <meta-data
42-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c75fad262b69372e31f80499b915284a\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
43                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
43-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c75fad262b69372e31f80499b915284a\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
44                android:value="androidx.startup" />
44-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c75fad262b69372e31f80499b915284a\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
45            <meta-data
45-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
46                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
46-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
47                android:value="androidx.startup" />
47-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
48        </provider>
49
50        <receiver
50-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
51            android:name="androidx.profileinstaller.ProfileInstallReceiver"
51-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
52            android:directBootAware="false"
52-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
53            android:enabled="true"
53-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
54            android:exported="true"
54-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
55            android:permission="android.permission.DUMP" >
55-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
56            <intent-filter>
56-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
57                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
57-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
57-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
58            </intent-filter>
59            <intent-filter>
59-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
60                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
60-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
60-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
61            </intent-filter>
62            <intent-filter>
62-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
63                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
63-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
63-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
64            </intent-filter>
65            <intent-filter>
65-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
66                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
66-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
66-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
67            </intent-filter>
68        </receiver>
69    </application>
70
71</manifest>
