<?xml version='1.0' encoding='UTF-8'?>
<androidx.webkit>
  <webkit versions="1.0.0-alpha1,1.0.0-alpha3,1.0.0-beta01,1.0.0-rc01,1.0.0-rc02,1.0.0,1.1.0-alpha01,1.1.0-alpha02,1.1.0-beta01,1.1.0-rc01,1.1.0,1.2.0-alpha01,1.2.0-beta01,1.2.0-rc01,1.2.0,1.3.0-alpha01,1.3.0-alpha02,1.3.0-alpha03,1.3.0-beta01,1.3.0-rc01,1.3.0-rc02,1.3.0,1.4.0-alpha01,1.4.0-beta01,1.4.0-rc01,1.4.0-rc02,1.4.0,1.5.0-alpha01,1.5.0-beta01,1.5.0-rc01,1.5.0,1.6.0-alpha01,1.6.0-alpha02,1.6.0-alpha03,1.6.0-beta01,1.6.0-rc01,1.6.0,1.6.1,1.7.0-alpha01,1.7.0-alpha02,1.7.0-alpha03,1.7.0-beta01,1.7.0-rc01,1.7.0,1.8.0-alpha01,1.8.0-beta01,1.8.0-rc01,1.8.0,1.9.0-alpha01,1.9.0-beta01,1.9.0-rc01,1.9.0,1.10.0-alpha01,1.10.0-beta01,1.10.0-rc01,1.10.0,1.11.0-alpha01,1.11.0-alpha02,1.11.0-beta01,1.11.0-rc01,1.11.0,1.12.0-alpha01,1.12.0-alpha02,1.12.0-beta01,1.12.0-rc01,1.12.0,1.12.1,1.13.0-alpha01,1.13.0-alpha02,1.13.0-alpha03,1.13.0-beta01,1.13.0-rc01,1.13.0,1.14.0-alpha01,1.14.0-beta01,1.14.0-rc01"/>
</androidx.webkit>
