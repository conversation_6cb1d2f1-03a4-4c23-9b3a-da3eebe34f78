<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About Calculator Tools</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .red-button {
            background-color: #f44336;
            color: white;
        }
        .red-button:hover {
            background-color: #d32f2f;
        }
    </style>
</head>
<body>
    <div class="container mx-auto px-4 py-8">
        <header class="text-center mb-10">
            <h1 class="text-3xl font-bold text-gray-800">About Calculator Tools</h1>
            <p class="text-gray-600 mt-2">View your calculation history</p>
        </header>
        
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-bold text-gray-800">Calculation History</h2>
                <span id="historyCount" class="text-sm text-gray-500">0 items</span>
            </div>
            <div id="calculationHistory" class="space-y-4">
                <!-- History items will be added here -->
                <p id="emptyHistory" class="text-gray-500 text-center py-4">No history yet</p>
            </div>
            <button id="clearHistory" class="mt-4 w-full py-2 px-4 bg-red-500 text-white rounded-lg font-bold hover:bg-red-600 transition duration-200">
                Clear History
            </button>
        </div>
        
        <div class="mt-10 text-center">
            <button onclick="navigateTo('index')" class="py-2 px-6 bg-gray-200 text-gray-800 rounded-lg font-bold hover:bg-gray-300 transition duration-200">
                <i class="fas fa-home mr-2"></i>Back to Home
            </button>
        </div>
    </div>

    <script>
        // DOM elements
        const calculationHistoryDiv = document.getElementById('calculationHistory');
        const historyCountSpan = document.getElementById('historyCount');
        const emptyHistoryP = document.getElementById('emptyHistory');
        const clearHistoryBtn = document.getElementById('clearHistory');
        
        // History management functions
        function saveToAndroidStorage(historyType, data) {
            if (window.Android) {
                window.Android.saveHistory(historyType, JSON.stringify(data));
            } else {
                localStorage.setItem(historyType, JSON.stringify(data));
            }
        }
        
        function loadFromAndroidStorage(historyType) {
            if (window.Android) {
                return JSON.parse(window.Android.loadHistory(historyType));
            } else {
                const data = localStorage.getItem(historyType);
                return data ? JSON.parse(data) : [];
            }
        }
        
        function clearHistoryFromAndroid(historyType) {
            if (window.Android) {
                window.Android.clearHistory(historyType);
            } else {
                localStorage.removeItem(historyType);
            }
        }
        
        // Navigation function
        function navigateTo(page) {
            if (window.Android) {
                window.Android.switchPage(page);
            } else {
                window.location.href = page + '.html';
            }
        }
        
        // Load and display history
        function loadAndDisplayHistory() {
            const historyItems = loadFromAndroidStorage('calculationHistory');
            
            if (historyItems.length === 0) {
                if (emptyHistoryP) emptyHistoryP.style.display = 'block';
                if (calculationHistoryDiv) calculationHistoryDiv.innerHTML = '';
                if (historyCountSpan) historyCountSpan.textContent = '0 items';
                return;
            }
            
            if (emptyHistoryP) emptyHistoryP.style.display = 'none';
            if (calculationHistoryDiv) {
                calculationHistoryDiv.innerHTML = '';
                
                historyItems.forEach(item => {
                    const historyItemEl = document.createElement('div');
                    historyItemEl.className = 'bg-gray-50 p-4 rounded-lg border border-gray-200';
                    historyItemEl.innerHTML = `
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="font-medium">${item.title}</p>
                                <p class="text-gray-600 text-sm">${item.description}</p>
                            </div>
                            <span class="text-xs text-gray-500">${item.date}</span>
                        </div>
                    `;
                    calculationHistoryDiv.appendChild(historyItemEl);
                });
            }
            
            if (historyCountSpan) historyCountSpan.textContent = `${historyItems.length} items`;
        }
        
        // Clear history
        if (clearHistoryBtn) {
            clearHistoryBtn.addEventListener('click', function() {
                if (confirm('Are you sure you want to clear all history?')) {
                    clearHistoryFromAndroid('calculationHistory');
                    loadAndDisplayHistory();
                }
            });
        }
        
        // Add a global clear all history button
        function addClearAllHistoryButton() {
            const container = document.querySelector('.container');
            if (!container) return;
            
            const clearAllBtn = document.createElement('button');
            clearAllBtn.className = 'w-full mt-6 py-3 px-4 red-button rounded-lg font-bold transition duration-200 flex items-center justify-center';
            clearAllBtn.innerHTML = '<i class="fas fa-trash-alt mr-2"></i> Clear All History';
            
            clearAllBtn.addEventListener('click', function() {
                if (confirm('Are you sure you want to clear all history data?')) {
                    // Clear Android storage
                    if (window.Android) {
                        window.Android.clearAllHistory();
                    } else {
                        localStorage.clear();
                    }
                    loadAndDisplayHistory();
                }
            });
            
            container.appendChild(clearAllBtn);
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadAndDisplayHistory();
            addClearAllHistoryButton();
        });
    </script>
</body>
</html>


