1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.scalper"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\Documents\apk\scalper\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\Users\<USER>\Documents\apk\scalper\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\Users\<USER>\Documents\apk\scalper\app\src\main\AndroidManifest.xml:6:5-79
12-->C:\Users\<USER>\Documents\apk\scalper\app\src\main\AndroidManifest.xml:6:22-76
13
14    <permission
14-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ac9a5a6a36d139f4d41d9656ee6c4dd\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
15        android:name="com.example.scalper.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
15-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ac9a5a6a36d139f4d41d9656ee6c4dd\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
16        android:protectionLevel="signature" />
16-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ac9a5a6a36d139f4d41d9656ee6c4dd\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
17
18    <uses-permission android:name="com.example.scalper.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
18-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ac9a5a6a36d139f4d41d9656ee6c4dd\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
18-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ac9a5a6a36d139f4d41d9656ee6c4dd\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
19
20    <application
20-->C:\Users\<USER>\Documents\apk\scalper\app\src\main\AndroidManifest.xml:8:5-20:19
21        android:allowBackup="true"
21-->C:\Users\<USER>\Documents\apk\scalper\app\src\main\AndroidManifest.xml:9:9-35
22        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
22-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ac9a5a6a36d139f4d41d9656ee6c4dd\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
23        android:dataExtractionRules="@xml/data_extraction_rules"
23-->C:\Users\<USER>\Documents\apk\scalper\app\src\main\AndroidManifest.xml:10:9-65
24        android:extractNativeLibs="false"
25        android:fullBackupContent="@xml/backup_rules"
25-->C:\Users\<USER>\Documents\apk\scalper\app\src\main\AndroidManifest.xml:11:9-54
26        android:icon="@mipmap/ic_launcher"
26-->C:\Users\<USER>\Documents\apk\scalper\app\src\main\AndroidManifest.xml:12:9-43
27        android:label="@string/app_name"
27-->C:\Users\<USER>\Documents\apk\scalper\app\src\main\AndroidManifest.xml:13:9-41
28        android:roundIcon="@mipmap/ic_launcher_round"
28-->C:\Users\<USER>\Documents\apk\scalper\app\src\main\AndroidManifest.xml:14:9-54
29        android:supportsRtl="true"
29-->C:\Users\<USER>\Documents\apk\scalper\app\src\main\AndroidManifest.xml:15:9-35
30        android:theme="@style/Theme.Scalper"
30-->C:\Users\<USER>\Documents\apk\scalper\app\src\main\AndroidManifest.xml:16:9-45
31        android:usesCleartextTraffic="true" >
31-->C:\Users\<USER>\Documents\apk\scalper\app\src\main\AndroidManifest.xml:17:9-44
32
33        <!-- Activity declaration -->
34        <provider
34-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc7c2f5ce3deab900ab6468ebed00762\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
35            android:name="androidx.startup.InitializationProvider"
35-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc7c2f5ce3deab900ab6468ebed00762\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
36            android:authorities="com.example.scalper.androidx-startup"
36-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc7c2f5ce3deab900ab6468ebed00762\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
37            android:exported="false" >
37-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc7c2f5ce3deab900ab6468ebed00762\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
38            <meta-data
38-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc7c2f5ce3deab900ab6468ebed00762\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
39                android:name="androidx.emoji2.text.EmojiCompatInitializer"
39-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc7c2f5ce3deab900ab6468ebed00762\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
40                android:value="androidx.startup" />
40-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bc7c2f5ce3deab900ab6468ebed00762\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
41            <meta-data
41-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c75fad262b69372e31f80499b915284a\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
42                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
42-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c75fad262b69372e31f80499b915284a\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
43                android:value="androidx.startup" />
43-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c75fad262b69372e31f80499b915284a\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
44            <meta-data
44-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
45                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
45-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
46                android:value="androidx.startup" />
46-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
47        </provider>
48
49        <receiver
49-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
50            android:name="androidx.profileinstaller.ProfileInstallReceiver"
50-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
51            android:directBootAware="false"
51-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
52            android:enabled="true"
52-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
53            android:exported="true"
53-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
54            android:permission="android.permission.DUMP" >
54-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
55            <intent-filter>
55-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
56                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
56-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
56-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
57            </intent-filter>
58            <intent-filter>
58-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
59                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
59-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
59-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
60            </intent-filter>
61            <intent-filter>
61-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
62                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
62-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
62-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
63            </intent-filter>
64            <intent-filter>
64-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
65                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
65-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
65-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\518c1f36c8ab7b267f611a769e93b1cd\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
66            </intent-filter>
67        </receiver>
68    </application>
69
70</manifest>
