<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About Scalper</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header>
        <h1>About Scalper</h1>
        <nav>
            <ul>
                <li><a href="javascript:Android.switchPage('index')">Home</a></li>
                <li><a href="javascript:Android.switchPage('about')">About</a></li>
            </ul>
        </nav>
    </header>
    
    <main>
        <section>
            <h2>Our Story</h2>
            <p>Scalper is a powerful trading assistant app designed to help you make informed trading decisions. Our app provides real-time market data, analysis tools, and trading insights to help you succeed in the market.</p>
            
            <h3>Features</h3>
            <ul>
                <li>Real-time market data</li>
                <li>Trading analysis tools</li>
                <li>Market insights</li>
                <li>Portfolio tracking</li>
            </ul>
        </section>
    </main>
    
    <footer>
        <p>&copy; 2024 Scalper App</p>
    </footer>

    <script>
        // Example of using the Android interface
        function showAboutMessage() {
            Android.showToast("Welcome to About page!");
        }
        // Call when page loads
        window.onload = showAboutMessage;
    </script>
</body>
</html>