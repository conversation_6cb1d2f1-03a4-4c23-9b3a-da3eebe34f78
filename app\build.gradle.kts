/**
 * Build configuration for the Scalper Android application
 * This file defines the build settings, dependencies, and compilation options
 */

// Apply required plugins for Android application development
plugins {
    alias(libs.plugins.android.application)  // Android Application plugin
    alias(libs.plugins.kotlin.android)       // Kotlin Android plugin
}

// Android-specific build configuration
android {
    namespace = "com.example.scalper"  // Package namespace for the application
    compileSdk = 35                    // SDK version used for compilation

    // Default configuration applied to all build variants
    defaultConfig {
        applicationId = "com.example.scalper"  // Unique identifier for the app
        minSdk = 24        // Minimum Android API level supported (Android 7.0)
        targetSdk = 35     // Target Android API level (Android 14)
        versionCode = 1    // Internal version number for app updates
        versionName = "1.0" // User-visible version string
    }

    // Build type configurations for different app variants
    buildTypes {
        release {
            isMinifyEnabled = false  // Disable code minification for easier debugging
            // ProGuard configuration files for code obfuscation and optimization
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }

    // Java compilation options - Fix for WebView JavaScript interface
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11  // Java source compatibility
        targetCompatibility = JavaVersion.VERSION_11  // Java target compatibility
    }

    // Kotlin compilation options
    kotlinOptions {
        jvmTarget = "11"  // Target JVM version for Kotlin compilation
    }
}

// Project dependencies
dependencies {
    // AndroidX Core libraries
    implementation(libs.androidx.core.ktx)           // Kotlin extensions for Android
    implementation(libs.androidx.appcompat)          // Backward compatibility support
    implementation(libs.material)                    // Material Design components
    implementation(libs.androidx.activity)           // Activity lifecycle management
    implementation(libs.androidx.constraintlayout)   // Constraint-based layouts

    // WebView support for enhanced web functionality
    implementation("androidx.webkit:webkit:1.8.0")

    // Testing dependencies
    testImplementation(libs.junit)                        // Unit testing framework
    androidTestImplementation(libs.androidx.junit)       // Android-specific JUnit extensions
    androidTestImplementation(libs.androidx.espresso.core) // UI testing framework
}




