package com.example.scalper

import android.content.Context
import android.webkit.JavascriptInterface
import android.widget.Toast

class WebAppInterface(private val context: Context) {
    
    @JavascriptInterface
    fun showToast(message: String) {
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
    }
    
    @JavascriptInterface
    fun saveHistory(historyType: String, data: String) {
        val sharedPref = context.getSharedPreferences("ScalperAppPrefs", Context.MODE_PRIVATE)
        with(sharedPref.edit()) {
            putString(historyType, data)
            apply()
        }
    }
    
    @JavascriptInterface
    fun loadHistory(historyType: String): String {
        val sharedPref = context.getSharedPreferences("ScalperAppPrefs", Context.MODE_PRIVATE)
        return sharedPref.getString(historyType, "[]") ?: "[]"
    }
    
    @JavascriptInterface
    fun clearHistory(historyType: String) {
        val sharedPref = context.getSharedPreferences("ScalperAppPrefs", Context.MODE_PRIVATE)
        with(sharedPref.edit()) {
            remove(historyType)
            apply()
        }
    }
    
    @JavascriptInterface
    fun clearAllHistory() {
        val sharedPref = context.getSharedPreferences("ScalperAppPrefs", Context.MODE_PRIVATE)
        with(sharedPref.edit()) {
            clear()
            apply()
        }
    }
    
    @JavascriptInterface
    fun switchPage(page: String) {
        if (context is MainActivity) {
            context.loadPage(page)
        }
    }
}