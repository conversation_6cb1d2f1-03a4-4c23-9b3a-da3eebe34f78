  SuppressLint android.annotation  R android.app.Activity  WebAppInterface android.app.Activity  endsWith android.app.Activity  onCreate android.app.Activity  Context android.content  MODE_PRIVATE android.content.Context  R android.content.Context  WebAppInterface android.content.Context  endsWith android.content.Context  getSharedPreferences android.content.Context  R android.content.ContextWrapper  WebAppInterface android.content.ContextWrapper  endsWith android.content.ContextWrapper  edit !android.content.SharedPreferences  	getString !android.content.SharedPreferences  apply (android.content.SharedPreferences.Editor  clear (android.content.SharedPreferences.Editor  	putString (android.content.SharedPreferences.Editor  remove (android.content.SharedPreferences.Editor  Bundle 
android.os  R  android.view.ContextThemeWrapper  WebAppInterface  android.view.ContextThemeWrapper  endsWith  android.view.ContextThemeWrapper  JavascriptInterface android.webkit  WebSettings android.webkit  WebView android.webkit  allowContentAccess android.webkit.WebSettings  allowFileAccess android.webkit.WebSettings  domStorageEnabled android.webkit.WebSettings  javaScriptEnabled android.webkit.WebSettings  addJavascriptInterface android.webkit.WebView  loadUrl android.webkit.WebView  settings android.webkit.WebView  url android.webkit.WebView  Toast android.widget  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ComponentActivity androidx.activity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  R #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  SuppressLint #androidx.activity.ComponentActivity  WebAppInterface #androidx.activity.ComponentActivity  WebView #androidx.activity.ComponentActivity  endsWith #androidx.activity.ComponentActivity  
onBackPressed #androidx.activity.ComponentActivity  R -androidx.activity.ComponentActivity.Companion  WebAppInterface -androidx.activity.ComponentActivity.Companion  endsWith -androidx.activity.ComponentActivity.Companion  AppCompatActivity androidx.appcompat.app  R (androidx.appcompat.app.AppCompatActivity  WebAppInterface (androidx.appcompat.app.AppCompatActivity  endsWith (androidx.appcompat.app.AppCompatActivity  findViewById (androidx.appcompat.app.AppCompatActivity  
onBackPressed (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  Bundle #androidx.core.app.ComponentActivity  R #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  SuppressLint #androidx.core.app.ComponentActivity  WebAppInterface #androidx.core.app.ComponentActivity  WebView #androidx.core.app.ComponentActivity  endsWith #androidx.core.app.ComponentActivity  R &androidx.fragment.app.FragmentActivity  WebAppInterface &androidx.fragment.app.FragmentActivity  endsWith &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  AppCompatActivity com.example.scalper  Bundle com.example.scalper  Context com.example.scalper  JavascriptInterface com.example.scalper  MainActivity com.example.scalper  R com.example.scalper  String com.example.scalper  SuppressLint com.example.scalper  Toast com.example.scalper  WebAppInterface com.example.scalper  WebView com.example.scalper  endsWith com.example.scalper  with com.example.scalper  R  com.example.scalper.MainActivity  WebAppInterface  com.example.scalper.MainActivity  endsWith  com.example.scalper.MainActivity  findViewById  com.example.scalper.MainActivity  loadPage  com.example.scalper.MainActivity  setContentView  com.example.scalper.MainActivity  webView  com.example.scalper.MainActivity  webView com.example.scalper.R.id  
activity_main com.example.scalper.R.layout  Context #com.example.scalper.WebAppInterface  Toast #com.example.scalper.WebAppInterface  context #com.example.scalper.WebAppInterface  with #com.example.scalper.WebAppInterface  	Function1 kotlin  with kotlin  endsWith 
kotlin.String  endsWith 	kotlin.io  endsWith kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 