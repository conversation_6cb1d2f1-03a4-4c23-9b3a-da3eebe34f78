"-Xallow-no-source-files" "-classpath" "C:\\Users\\<USER>\\Documents\\apk\\scalper\\app\\build\\intermediates\\compile_and_runtime_not_namespaced_r_class_jar\\release\\processReleaseResources\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\03b221dcc248dbe921fba196d763193a\\transformed\\material-1.12.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e910371b880876c38a87277d78e3fdf0\\transformed\\appcompat-resources-1.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8be5a5431f9add8c9086fe3015a6f348\\transformed\\appcompat-1.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\43db71024170a2537cdb0a95f795e209\\transformed\\viewpager2-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4aa04e46a5ac1ef8c6b7235d0ecaa50e\\transformed\\fragment-1.5.4-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b40de57786cdbf2c5de9ce4b050dbd94\\transformed\\activity-1.10.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\379a57deb79cca767d15973852318b24\\transformed\\webkit-1.8.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d24c174880e6c59262758793de0b161f\\transformed\\drawerlayout-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ff8717cbd3aea24d29a53d69f69fbed5\\transformed\\coordinatorlayout-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a8c3ec6d761cc244c1a35ec7080c1b6f\\transformed\\dynamicanimation-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\63081015396e08415719add65be3127a\\transformed\\recyclerview-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4d56fa608d2f6a32d60486256c04ee8d\\transformed\\transition-1.5.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ee2f19cd578f8c2b627cc703ce7c2563\\transformed\\vectordrawable-animated-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f88fd6f0cf4eedb8fa98d33af3e23cc1\\transformed\\vectordrawable-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\042b567ecaf7eae298fcd16a7b5f07d4\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9c71281f7941dc916f64b1df22bdd7bb\\transformed\\customview-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e04a0258014072d108fc9592059a6526\\transformed\\legacy-support-core-utils-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\5c20f5d9ccbbd7748084b20d85e6fde5\\transformed\\loader-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fd872a2e4c6b7cf2643bb3c7b62814b1\\transformed\\core-1.16.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4c889be59e6611dab759f63acea799e8\\transformed\\lifecycle-livedata-2.6.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\48121408b3311e620b04f55bf9dbc1e4\\transformed\\lifecycle-livedata-core-2.6.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2c2516bd5b0f17730e6a9d2f8f05b8ac\\transformed\\lifecycle-viewmodel-2.6.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.6.2\\10f354fdb64868baecd67128560c5a0d6312c495\\lifecycle-common-2.6.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\17f48eb848be6b4f5b4b0d07053ce1e2\\transformed\\lifecycle-runtime-2.6.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a3420a03251a20334e32d1f9b1e15e46\\transformed\\lifecycle-viewmodel-savedstate-2.6.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\67eff6096ea9ee1cacb7833cd84d6802\\transformed\\core-ktx-1.16.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ad25d72ae1cf5968c9c24bb1120fd6e7\\transformed\\savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d8fadc23d1f032fa02aecd1e8079e489\\transformed\\annotation-experimental-1.4.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\25c502d740230c2709520a94159bbcc8\\transformed\\core-viewtree-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\96f9b84f44f58b17471bf280468959bd\\transformed\\cursoradapter-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8e15a90d514d80e4b2309c9e2057243a\\transformed\\cardview-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\180f44319949164d79acfbcdcdbf04a9\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\486549ea8559093aeeb7222dcf96188f\\transformed\\core-runtime-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\502b27d4ed97d488e895bed3c4e6576e\\transformed\\interpolator-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection-jvm\\1.4.2\\bc60b5568a66d765a9fe8e266fd0c6c727e0b50b\\collection-jvm-1.4.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f928d0cba11db1aea00394138a34296e\\transformed\\documentfile-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\291498b70b13a09c77281d56dc174c83\\transformed\\localbroadcastmanager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\47089ad3a39a4c23fb766cc31a85e7da\\transformed\\print-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.annotation\\annotation-jvm\\1.8.1\\b8a16fe526014b7941c1debaccaf9c5153692dbb\\annotation-jvm-1.8.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlinx\\kotlinx-coroutines-core-jvm\\1.7.3\\2b09627576f0989a436a00a4a54b55fa5026fb86\\kotlinx-coroutines-core-jvm-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlinx\\kotlinx-coroutines-android\\1.7.3\\38d9cad3a0b03a10453b56577984bdeb48edeed5\\kotlinx-coroutines-android-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib-jdk8\\1.8.22\\b25c86d47d6b962b9cf0f8c3f320c8a10eea3dd1\\kotlin-stdlib-jdk8-1.8.22.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib-jdk7\\1.8.22\\4dabb8248310d833bb6a8b516024a91fd3d275c\\kotlin-stdlib-jdk7-1.8.22.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains.kotlin\\kotlin-stdlib\\2.0.21\\618b539767b4899b4660a83006e052b63f1db551\\kotlin-stdlib-2.0.21.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0b8d0b469ec016deb2f676e87b05fbdf\\transformed\\constraintlayout-2.1.4-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jetbrains\\annotations\\23.0.0\\8cc20c07506ec18e0834947b84a864bfc094484e\\annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jspecify\\jspecify\\1.0.0\\7425a601c1c7ec76645a78d22b8c6a627edee507\\jspecify-1.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.resourceinspection\\resourceinspection-annotation\\1.0.1\\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\\resourceinspection-annotation-1.0.1.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platforms\\android-35\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\build-tools\\35.0.0\\core-lambda-stubs.jar" "-d" "C:\\Users\\<USER>\\Documents\\apk\\scalper\\app\\build\\tmp\\kotlin-classes\\release" "-jvm-target" "11" "-module-name" "app_release" "-no-jdk" "-no-reflect" "-no-stdlib" "C:\\Users\\<USER>\\Documents\\apk\\scalper\\app\\src\\main\\java\\com\\example\\scalper\\MainActivity.kt" "C:\\Users\\<USER>\\Documents\\apk\\scalper\\app\\src\\main\\java\\com\\example\\scalper\\WebAppInterface.kt"