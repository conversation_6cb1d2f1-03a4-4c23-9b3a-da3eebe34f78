package com.example.scalper

import android.annotation.SuppressLint
import android.os.Bundle
import android.webkit.WebView
import androidx.appcompat.app.AppCompatActivity

class MainActivity : AppCompatActivity() {
    private lateinit var webView: WebView

    @SuppressLint("SetJavaScriptEnabled")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        
        webView = findViewById(R.id.webView)
        webView.settings.javaScriptEnabled = true
        webView.settings.domStorageEnabled = true
        webView.settings.allowFileAccess = true
        webView.settings.allowContentAccess = true
        
        // Add JavaScript interface
        webView.addJavascriptInterface(WebAppInterface(this), "Android")
        
        // Load the default HTML file
        webView.loadUrl("file:///android_asset/index.html")
    }
    
    fun loadPage(page: String) {
        webView.loadUrl("file:///android_asset/$page.html")
    }
    
    override fun onBackPressed() {
        if (webView.url?.endsWith("index.html") == false) {
            loadPage("index")
        } else {
            super.onBackPressed()
        }
    }
}



