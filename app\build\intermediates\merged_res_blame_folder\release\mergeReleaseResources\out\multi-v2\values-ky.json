{"logs": [{"outputFile": "com.example.scalper.app-mergeReleaseResources-32:/values-ky/values-ky.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fabfe20e41a903b43fd385de888f3092\\transformed\\material-1.12.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,348,433,518,633,743,844,985,1069,1129,1193,1287,1357,1418,1505,1568,1632,1691,1765,1827,1881,1998,2056,2117,2171,2245,2367,2451,2530,2630,2716,2812,2944,3022,3100,3229,3318,3398,3459,3514,3580,3649,3726,3797,3878,3952,4028,4118,4191,4293,4378,4457,4547,4639,4713,4798,4888,4940,5024,5089,5174,5259,5321,5385,5448,5517,5634,5742,5842,5946,6011,6070,6152,6238,6314", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,82,84,84,114,109,100,140,83,59,63,93,69,60,86,62,63,58,73,61,53,116,57,60,53,73,121,83,78,99,85,95,131,77,77,128,88,79,60,54,65,68,76,70,80,73,75,89,72,101,84,78,89,91,73,84,89,51,83,64,84,84,61,63,62,68,116,107,99,103,64,58,81,85,75,82", "endOffsets": "260,343,428,513,628,738,839,980,1064,1124,1188,1282,1352,1413,1500,1563,1627,1686,1760,1822,1876,1993,2051,2112,2166,2240,2362,2446,2525,2625,2711,2807,2939,3017,3095,3224,3313,3393,3454,3509,3575,3644,3721,3792,3873,3947,4023,4113,4186,4288,4373,4452,4542,4634,4708,4793,4883,4935,5019,5084,5169,5254,5316,5380,5443,5512,5629,5737,5837,5941,6006,6065,6147,6233,6309,6392"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3027,3110,3195,3280,3395,4234,4335,4476,4560,4620,4684,4778,4848,4909,4996,5059,5123,5182,5256,5318,5372,5489,5547,5608,5662,5736,5858,5942,6021,6121,6207,6303,6435,6513,6591,6720,6809,6889,6950,7005,7071,7140,7217,7288,7369,7443,7519,7609,7682,7784,7869,7948,8038,8130,8204,8289,8379,8431,8515,8580,8665,8750,8812,8876,8939,9008,9125,9233,9333,9437,9502,9561,9725,9811,9887", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "endColumns": "12,82,84,84,114,109,100,140,83,59,63,93,69,60,86,62,63,58,73,61,53,116,57,60,53,73,121,83,78,99,85,95,131,77,77,128,88,79,60,54,65,68,76,70,80,73,75,89,72,101,84,78,89,91,73,84,89,51,83,64,84,84,61,63,62,68,116,107,99,103,64,58,81,85,75,82", "endOffsets": "310,3105,3190,3275,3390,3500,4330,4471,4555,4615,4679,4773,4843,4904,4991,5054,5118,5177,5251,5313,5367,5484,5542,5603,5657,5731,5853,5937,6016,6116,6202,6298,6430,6508,6586,6715,6804,6884,6945,7000,7066,7135,7212,7283,7364,7438,7514,7604,7677,7779,7864,7943,8033,8125,8199,8284,8374,8426,8510,8575,8660,8745,8807,8871,8934,9003,9120,9228,9328,9432,9497,9556,9638,9806,9882,9965"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f69bd16c9ebab485e86d956506a6d727\\transformed\\appcompat-1.7.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,901,992,1085,1180,1274,1374,1467,1562,1657,1748,1839,1920,2026,2131,2229,2336,2439,2554,2715,2817", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "211,320,432,517,622,739,818,896,987,1080,1175,1269,1369,1462,1557,1652,1743,1834,1915,2021,2126,2224,2331,2434,2549,2710,2812,2894"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,426,535,647,732,837,954,1033,1111,1202,1295,1390,1484,1584,1677,1772,1867,1958,2049,2130,2236,2341,2439,2546,2649,2764,2925,9643", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "421,530,642,727,832,949,1028,1106,1197,1290,1385,1479,1579,1672,1767,1862,1953,2044,2125,2231,2336,2434,2541,2644,2759,2920,3022,9720"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1ac9a5a6a36d139f4d41d9656ee6c4dd\\transformed\\core-1.16.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,569,673,784", "endColumns": "99,101,102,106,101,103,110,100", "endOffsets": "150,252,355,462,564,668,779,880"}, "to": {"startLines": "38,39,40,41,42,43,44,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3505,3605,3707,3810,3917,4019,4123,9970", "endColumns": "99,101,102,106,101,103,110,100", "endOffsets": "3600,3702,3805,3912,4014,4118,4229,10066"}}]}]}